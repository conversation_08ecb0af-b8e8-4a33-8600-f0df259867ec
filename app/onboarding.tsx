import React, { useState } from 'react';
import { View, Alert, Text, useColorScheme } from 'react-native';
import { router } from 'expo-router';
import { Question } from '@/types/questions';
import { QuestionCard } from '@/app/components/QuestionCard';
import { OnboardingSummaryCard } from '@/app/components/OnboardingSummaryCard';
import { createOnboardingStyles } from '@/app/styles/OnboardingStyles';
import { getThemeColors } from '@/app/styles/Theme';
import { useAuth } from '@/app/contexts/AuthContext';
import { firestoreRepository, FirestoreCollections } from '@/repositories/firestoreRepository';

const questions: Question[] = [
  {
    id: "allergies",
    text: "Select any food allergies you have:",
    allowMultiple: true,
    options: [
      { value: "peanuts", label: "Peanuts", emoji: "🥜" },
      { value: "tree_nuts", label: "Tree Nuts", emoji: "🌰" },
      { value: "milk", label: "Milk", emoji: "🥛" },
      { value: "eggs", label: "Eggs", emoji: "🥚" },
      { value: "soy", label: "Soy", emoji: "🫘" },
      { value: "wheat", label: "Wheat", emoji: "🌾" },
      { value: "fish", label: "Fish", emoji: "🐟" },
      { value: "shellfish", label: "Shellfish", emoji: "🦐" },
      { value: "other_allergies", label: "Other", emoji: "⚠️" },
    ],
  },
  {
    id: "cooking_time",
    text: "How much time do you have for cooking?",
    options: [
      { value: "<15", label: "< 15 min", emoji: "⚡" },
      { value: "15-30", label: "15-30 min", emoji: "⏱️" },
      { value: "30-45", label: "30-45 min", emoji: "⌛" },
      { value: "45-60", label: "45-60 min", emoji: "⏳" },
      { value: ">60", label: "> 60 min", emoji: "🕰️" },
    ],
  },
  {
    id: "cooking_experience",
    text: "What's your cooking experience level?",
    options: [
      { value: "beginner", label: "Beginner", emoji: "🌱" },
      { value: "novice", label: "Novice", emoji: "🌿" },
      { value: "intermediate", label: "Intermediate", emoji: "🌳" },
      { value: "advanced", label: "Advanced", emoji: "🌲" },
      { value: "expert", label: "Expert", emoji: "🎯" },
    ],
  },
  {
    id: "dietary_goals",
    text: "What are your main dietary goals?",
    allowMultiple: false,
    options: [
      { value: "weight_management", label: "Weight Management", emoji: "⚖️" },
      { value: "gut_health", label: "Gut Health", emoji: "🌿" },
      { value: "build_muscle", label: "Build Muscle", emoji: "💪" },
      { value: "eat_healthier", label: "Eat Healthier", emoji: "🥗" },
      { value: "increase_energy", label: "Increase Energy", emoji: "⚡" },
      { value: "none", label: "None currently", emoji: "🤷" },
    ],
  },
  {
    id: "calorie_goal",
    text: "What's your daily calorie goal? (Optional)",
    options: [
      { value: "no_goal", label: "No Goal", emoji: "🍽️" },
      { value: "1500-1800", label: "1500-1800", emoji: "🥗" },
      { value: "1800-2000", label: "1800-2000", emoji: "🥙" },
      { value: "2000-2500", label: "2000-2500", emoji: "🍱" },
      { value: "2500+", label: "2500+", emoji: "🍖" },
    ],
  },
  {
    id: "specific_diet",
    text: "Are you following any specific diet?",
    options: [
      { value: "no_diet", label: "No Diet", emoji: "🍽️" },
      { value: "vegetarian", label: "Vegetarian", emoji: "🥬" },
      { value: "vegan", label: "Vegan", emoji: "🌱" },
      { value: "pescatarian", label: "Pescatarian", emoji: "🐟" },
      { value: "mediterranean", label: "Mediterranean", emoji: "🫒" },
      { value: "keto", label: "Keto", emoji: "🥩" },
      { value: "paleo", label: "Paleo", emoji: "🥓" },
      { value: "gluten_free", label: "Gluten-Free", emoji: "🌾" },
      { value: "dairy_free", label: "Dairy-Free", emoji: "🥛" },
      { value: "low_carb", label: "Low-Carb", emoji: "🥗" },
      { value: "other_diet", label: "Other", emoji: "🎯" },
    ],
  },
];

interface OnboardingGuiState {
  currentQuestionIndex: number;
  answers: Record<string, string | string[]>;
  isComplete: boolean;
}

// Helper function to transform answers to the target Firebase schema
const transformAnswersToFirebaseSchema = (answers: Record<string, any>) => {
  const transformed: Record<string, any> = {};

  // 1. allergies
  if (answers.allergies) {
    transformed.allergies = Array.isArray(answers.allergies) ? answers.allergies : [answers.allergies];
  } else {
    transformed.allergies = []; // Default to empty array if no allergies selected
  }

  // 2. calories (from calorie_goal)
  const calorieGoal = answers.calorie_goal as string;
  if (calorieGoal === 'no_goal') {
    transformed.calories = 0; // Or null, if your schema/backend prefers
  } else if (calorieGoal === '1500-1800') {
    transformed.calories = 1500;
  } else if (calorieGoal === '1800-2000') {
    transformed.calories = 1800;
  } else if (calorieGoal === '2000-2500') {
    transformed.calories = 2000;
  } else if (calorieGoal === '2500+') {
    transformed.calories = 2500;
  } else {
    transformed.calories = 0; // Default if undefined or unexpected value
  }

  // 3. diet (from specific_diet)
  transformed.diet = answers.specific_diet || 'none'; // Default to 'none' if not answered

  // 4. experience (from cooking_experience)
  transformed.experience = answers.cooking_experience || 'beginner'; // Default to 'beginner'

  // 5. goals - Now directly from the new dietary_goals question
  transformed.goals = answers.dietary_goals || 'none'; // Default to 'none' if not answered

  // 6. timeToCook (from cooking_time)
  const cookingTime = answers.cooking_time as string;
  switch (cookingTime) {
    case '<15':
      transformed.timeToCook = 'quick';
      break;
    case '15-30':
      transformed.timeToCook = 'short';
      break;
    case '30-45':
      transformed.timeToCook = 'medium';
      break;
    case '45-60':
      transformed.timeToCook = 'long';
      break;
    case '>60':
      transformed.timeToCook = 'flexible';
      break;
    default:
      transformed.timeToCook = 'medium'; // Default cooking time
  }

  return transformed;
};

export default function OnboardingScreen() {
  const colorScheme = useColorScheme() || 'light';
  const colors = getThemeColors(colorScheme);
  const styles = createOnboardingStyles(colors);
  const { user } = useAuth();

  const [state, setState] = useState<OnboardingGuiState>({
    currentQuestionIndex: 0,
    answers: {},
    isComplete: false,
  });

  const handleAnswer = (answerValue: string | string[]) => {
    const currentQuestionId = questions[state.currentQuestionIndex].id;
    const newAnswers = { ...state.answers, [currentQuestionId]: answerValue };

    if (state.currentQuestionIndex === questions.length - 1) {
      console.log("Onboarding Answers (Raw):", JSON.stringify(newAnswers));
      setState(prevState => ({ ...prevState, answers: newAnswers, isComplete: true }));
    } else {
      setState(prevState => ({
        ...prevState,
        currentQuestionIndex: prevState.currentQuestionIndex + 1,
        answers: newAnswers,
      }));
    }
  };

  const handleBack = () => {
    if (state.currentQuestionIndex > 0) {
      setState(prevState => ({
        ...prevState,
        currentQuestionIndex: prevState.currentQuestionIndex - 1,
      }));
    }
  };

  const handleProceedToCamera = async () => {
    if (!user) {
      Alert.alert("Error", "User not authenticated. Cannot save preferences.");
      return;
    }
    try {
      const transformedPreferences = transformAnswersToFirebaseSchema(state.answers);
      console.log("Transformed Preferences for Firebase:", JSON.stringify(transformedPreferences));

      await firestoreRepository.addOrReplaceDocument(
        FirestoreCollections.DIET_PREFERENCES,
        user.uid,
        transformedPreferences // Use the transformed data
      );
      Alert.alert('Preferences Saved!', 'Moving to camera...', [
        { text: "OK", onPress: () => router.replace('/camera') }
      ]);
    } catch (error) {
      console.error("Error saving onboarding preferences:", error);
      Alert.alert("Error", "Failed to save preferences. Please try again.");
    }
  };

  if (state.isComplete) {
    return (
      <OnboardingSummaryCard
        answers={state.answers}
        onProceed={handleProceedToCamera}
      />
    );
  }

  const currentQuestion = questions[state.currentQuestionIndex];
  if (!currentQuestion) {
    return (
      <View style={styles.containerCentered}>
        <Text style={styles.completionText}>Loading question...</Text>
      </View>
    );
  }
  const selectedAnswersForCurrentQuestion = state.answers[currentQuestion.id];
  let initialSelectedValues: string[] = [];
  if (selectedAnswersForCurrentQuestion) {
    initialSelectedValues = Array.isArray(selectedAnswersForCurrentQuestion)
      ? selectedAnswersForCurrentQuestion
      : [selectedAnswersForCurrentQuestion];
  }

  return (
    <View style={styles.container}>
      <QuestionCard
        question={currentQuestion}
        onAnswer={handleAnswer}
        onBack={handleBack}
        currentIndex={state.currentQuestionIndex}
        totalQuestions={questions.length}
        canGoBack={state.currentQuestionIndex > 0}
        selectedValues={initialSelectedValues}
      />
    </View>
  );
}