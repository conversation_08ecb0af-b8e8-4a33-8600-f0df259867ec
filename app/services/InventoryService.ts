import { auth } from '@/firebase/firebaseConfig';
import { firestoreRepository, FirestoreCollections } from '@/repositories/firestoreRepository';
import { InventoryItem, InventoryCategory } from '@/app/components/types';
import { LlmService } from '@/app/services/LlmService';
import { ResponseInputItem } from 'openai/resources/responses/responses';
import { Roles } from '@/constants/Types';
import { inventoryCategorizationSchema } from '@/schemas/inventoryCategorization';
import { inventoryCategorizationModel } from '@/constants/LlmConfigs';

/**
 * Service for managing inventory items
 */
export class InventoryService {
  /**
   * Get all inventory items for the current user
   */
  static async getUserInventory(): Promise<InventoryItem[]> {
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error('User not authenticated');
      }

      const inventoryDoc = await firestoreRepository.getDocument(FirestoreCollections.INVENTORY, user.uid);

      if (!inventoryDoc || !inventoryDoc.items) {
        return [];
      }

      return inventoryDoc.items as InventoryItem[];
    } catch (error) {
      console.error('Error getting user inventory:', error);
      return [];
    }
  }

  /**
   * Save inventory items for the current user
   */
  static async saveUserInventory(items: InventoryItem[]): Promise<void> {
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error('User not authenticated');
      }

      await firestoreRepository.addOrReplaceDocument(FirestoreCollections.INVENTORY, user.uid, { items });
    } catch (error) {
      console.error('Error saving user inventory:', error);
      throw error;
    }
  }

  /**
   * Add or update an inventory item
   */
  static async addOrUpdateItem(item: InventoryItem): Promise<void> {
    try {
      const inventory = await this.getUserInventory();

      // Check if item already exists
      const existingItemIndex = inventory.findIndex((i) => i.name.toLowerCase() === item.name.toLowerCase());

      if (existingItemIndex >= 0) {
        // Update existing item
        inventory[existingItemIndex] = {
          ...inventory[existingItemIndex],
          quantity: inventory[existingItemIndex].quantity === 0 ? item.quantity : inventory[existingItemIndex].quantity,
        };
      } else {
        // Add new item
        inventory.push({
          ...item,
          addedAt: Date.now(),
        });
      }

      await this.saveUserInventory(inventory);
    } catch (error) {
      console.error('Error adding/updating inventory item:', error);
      throw error;
    }
  }

  /**
   * Remove an inventory item
   */
  static async removeItem(itemName: string): Promise<void> {
    try {
      const inventory = await this.getUserInventory();
      const updatedInventory = inventory.filter((item) => item.name.toLowerCase() !== itemName.toLowerCase());
      await this.saveUserInventory(updatedInventory);
    } catch (error) {
      console.error('Error removing inventory item:', error);
      throw error;
    }
  }

  /**
   * Update item quantity
   */
  static async updateItemQuantity(itemName: string, quantity: number): Promise<void> {
    try {
      const inventory = await this.getUserInventory();
      const updatedInventory = inventory.map((item) => {
        if (item.name.toLowerCase() === itemName.toLowerCase()) {
          return { ...item, quantity };
        }
        return item;
      });
      await this.saveUserInventory(updatedInventory);
    } catch (error) {
      console.error('Error updating item quantity:', error);
      throw error;
    }
  }

  /**
   * Categorize inventory items using LLM
   */
  static async categorizeInventory(items: InventoryItem[]): Promise<InventoryCategory[]> {
    try {
      if (items.length === 0) {
        return [];
      }

      const instructions = `
        You are a helpful assistant that categorizes food items into logical groups.
        For example, you can categorize the items into fruits, vegetables, grains & cereals, meat, dairy & alternatives, fats & oils, sweets & snacks, condiments & spices, drinks, and other/miscellaneous items. etc.
        For each category, provide the appropriate emoji. If there are not items in a category, simply omit it from the response. If certain categories are not clear, feel free to create new categories.
        The 'items' property should be an array of objects with 'name' and 'quantity' properties.
        Return the result as a JSON object with a 'categories' property that contains an array of category objects, each with 'name', 'emoji', and 'items' properties.
      `;

      const input: ResponseInputItem[] = [
        {
          role: Roles.developer,
          content: `Here are the food items to categorize: ${JSON.stringify(items)}`,
        },
      ];

      const response = await LlmService.callLlmApi(
        inventoryCategorizationModel,
        instructions,
        input,
        0.1,
        inventoryCategorizationSchema,
        'categories'
      );

      const outputText = LlmService.extractOutputText(response);
      const parsedResponse = JSON.parse(outputText);
      const categories: InventoryCategory[] = parsedResponse.categories || [];
      return categories;
    } catch (error) {
      console.error('Error categorizing inventory:', JSON.stringify(error));

      return [
        {
          name: 'Other',
          emoji: '🍥',
          items: items,
        },
      ];
    }
  }
}

export default InventoryService;
