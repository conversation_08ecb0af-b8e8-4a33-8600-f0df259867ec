import React from 'react';
import { View, Text, StyleSheet, useColorScheme } from 'react-native';
import { getThemeColors, ThemeColors } from '@/app/styles/Theme';

export interface ProgressIndicatorProps {
    current: number;
    total: number;
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({ current, total }) => {
    const colorScheme = useColorScheme() || 'light';
    const colors = getThemeColors(colorScheme);
    const styles = createProgressIndicatorStyles(colors);

    return (
        <View style={styles.container}>
            <Text style={styles.text}>
                Question {current + 1} of {total}
            </Text>
            <View style={styles.progressBar}>
                <View
                    style={[
                        styles.progressFill,
                        { width: `${total > 0 ? ((current + 1) / total) * 100 : 0}%` }
                    ]}
                />
            </View>
        </View>
    );
};

const createProgressIndicatorStyles = (colors: ThemeColors) => StyleSheet.create({
    container: {
        paddingVertical: 16,
        paddingHorizontal: 24,
        width: '100%',
        backgroundColor: colors.background,
    },
    text: {
        fontSize: 14,
        color: colors.textSecondary,
        marginBottom: 8,
        textAlign: 'center',
    },
    progressBar: {
        height: 6,
        backgroundColor: colors.divider,
        borderRadius: 3,
        overflow: 'hidden',
    },
    progressFill: {
        height: '100%',
        backgroundColor: colors.accent,
        borderRadius: 3,
    },
}); 