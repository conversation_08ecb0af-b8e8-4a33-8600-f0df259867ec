from firebase_admin import initialize_app, auth as admin_auth, firestore
from firebase_functions import https_fn
from firebase_functions.https_fn import Request, Response
from openai import OpenAI
import os
import json
import requests
from urllib.parse import quote

# Initialize Firebase Admin SDK (for Firestore/Auth/etc. if needed)
app = initialize_app()
db = firestore.client()

@https_fn.on_request(secrets=["OPENAI_API_KEY"], timeout_sec=300)
def generate(req: Request) -> Response:
    id_token = req.headers.get("Authorization", "").replace("Bearer ", "")
    decoded_token = admin_auth.verify_id_token(id_token)
    uid = decoded_token["uid"]

    try:
        data = req.get_json(silent=True)

        if not data or "input" not in data:
            return Response(json.dumps({"error": "Missing 'input' in request body"}), status=400, headers={"Content-Type": "application/json"})

        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            return Response(json.dumps({"error": "OpenAI API key not found"}), status=500, headers={"Content-Type": "application/json"})

        client = OpenAI(api_key=api_key)

        # Create parameters dictionary with required fields
        params = {
            "model": data.get("model", "gpt-4.1"),
            "input": data["input"],
            "temperature": data.get("temperature", 1),
            "instructions": data.get("instructions"),
            "text": data.get("text", {"format": {"type": "text"}}),
            "user": uid
        }

        # Add previous_response_id if it exists in the request
        if "previous_response_id" in data and data["previous_response_id"]:
            params["previous_response_id"] = data["previous_response_id"]

        response = client.responses.create(**params)

        return Response(json.dumps(response.model_dump()), status=200, headers={"Content-Type": "application/json"})

    except Exception as e:
        return Response(json.dumps({"error": str(e)}), status=500, headers={"Content-Type": "application/json"})


@https_fn.on_request(secrets=["UNSPLASH_ACCESS_KEY"], timeout_sec=10)
def get_unsplash_image(req: Request) -> Response:
    # Verify authentication
    id_token = req.headers.get("Authorization", "").replace("Bearer ", "")
    try:
        decoded_token = admin_auth.verify_id_token(id_token)
        _ = decoded_token["uid"]
    except Exception as e:
        return Response(
            json.dumps({"error": "Unauthorized"}),
            status=401,
            headers={"Content-Type": "application/json"}
        )

    if req.method != "GET":
        return Response(
            json.dumps({"error": "Method not allowed. Only GET requests are supported."}),
            status=405,
            headers={"Content-Type": "application/json"}
        )

    try:
        query = req.args.get("query")

        if not query:
            return Response(
                json.dumps({"error": "Missing 'query' parameter"}),
                status=400,
                headers={"Content-Type": "application/json"}
            )

        # Get Unsplash API key from secrets
        unsplash_key = os.environ.get("UNSPLASH_ACCESS_KEY")
        if not unsplash_key:
            return Response(
                json.dumps({"error": "Unsplash API key not found"}),
                status=500,
                headers={"Content-Type": "application/json"}
            )

        # Call Unsplash API
        url = f"https://api.unsplash.com/search/photos?query={quote(query)}&per_page=1"
        headers = {
            "Authorization": f"Client-ID {unsplash_key}",
        }

        response = requests.get(url, headers=headers, timeout=10)

        if response.status_code != 200:
            return Response(
                json.dumps({
                    "error": f"Unsplash API returned status {response.status_code}",
                    "details": response.text
                }),
                status=response.status_code,
                headers={"Content-Type": "application/json"}
            )

        data = response.json()
        results = data.get("results", [])

        if not results:
            return Response(
                json.dumps({"error": "No images found for the given query"}),
                status=404,
                headers={"Content-Type": "application/json"}
            )

        image_url = results[0].get("urls", {}).get("regular")

        if not image_url:
            return Response(
                json.dumps({"error": "Image URL not found in API response"}),
                status=404,
                headers={"Content-Type": "application/json"}
            )

        return Response(
            json.dumps({"imageUrl": image_url}),
            status=200,
            headers={"Content-Type": "application/json"}
        )

    except requests.exceptions.Timeout:
        return Response(
            json.dumps({"error": "Request to Unsplash API timed out"}),
            status=408,
            headers={"Content-Type": "application/json"}
        )
    except requests.exceptions.RequestException as e:
        return Response(
            json.dumps({"error": f"Network error: {str(e)}"}),
            status=503,
            headers={"Content-Type": "application/json"}
        )
    except Exception as e:
        return Response(
            json.dumps({"error": f"Internal server error: {str(e)}"}),
            status=500,
            headers={"Content-Type": "application/json"}
        )


@https_fn.on_request(secrets=["OPENAI_API_KEY"], timeout_sec=600)
def generate_recipes(req: Request) -> Response:
    """
    Generate 30 recipes (10 breakfast, 10 lunch, 10 dinner) based on user's diet preferences
    after onboarding completion and store them in Firestore.
    """
    # Verify authentication
    id_token = req.headers.get("Authorization", "").replace("Bearer ", "")
    try:
        decoded_token = admin_auth.verify_id_token(id_token)
        uid = decoded_token["uid"]
    except Exception as e:
        return Response(
            json.dumps({"error": "Unauthorized"}),
            status=401,
            headers={"Content-Type": "application/json"}
        )

    if req.method != "POST":
        return Response(
            json.dumps({"error": "Method not allowed. Only POST requests are supported."}),
            status=405,
            headers={"Content-Type": "application/json"}
        )

    try:
        # Get user's diet preferences from Firestore
        diet_prefs_ref = db.collection('dietPreferences').document(uid)
        diet_prefs_doc = diet_prefs_ref.get()

        if not diet_prefs_doc.exists:
            return Response(
                json.dumps({"error": "Diet preferences not found. Please complete onboarding first."}),
                status=404,
                headers={"Content-Type": "application/json"}
            )

        diet_preferences = diet_prefs_doc.to_dict()

        # Get OpenAI API key
        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            return Response(
                json.dumps({"error": "OpenAI API key not found"}),
                status=500,
                headers={"Content-Type": "application/json"}
            )

        client = OpenAI(api_key=api_key)

        # Define the recipe generation schema
        recipe_generation_schema = {
            "type": "object",
            "properties": {
                "recipes": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "string"},
                            "title": {"type": "string"},
                            "timeInMinutes": {"type": "number"},
                            "calories": {"type": "number"},
                            "ingredients": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "name": {"type": "string"},
                                        "available": {"type": "boolean"}
                                    },
                                    "required": ["name", "available"],
                                    "additionalProperties": False
                                }
                            },
                            "instructions": {
                                "type": "object",
                                "properties": {
                                    "High level": {"type": "string"},
                                    "Detailed": {"type": "string"},
                                    "Teach mode": {"type": "string"}
                                },
                                "required": ["High level", "Detailed", "Teach mode"],
                                "additionalProperties": False
                            },
                            "imageQuery": {"type": "string"},
                            "mealType": {
                                "type": "string",
                                "enum": ["Breakfast", "Lunch", "Dinner", "Snacks", "Dessert"]
                            }
                        },
                        "required": ["id", "title", "timeInMinutes", "calories", "ingredients", "instructions", "imageQuery", "mealType"],
                        "additionalProperties": False
                    }
                }
            },
            "required": ["recipes"],
            "additionalProperties": False
        }

        # Create instructions for recipe generation
        instructions = f"""You are a professional chef and nutritionist. Generate detailed, practical recipes based on the user's dietary preferences.

User's dietary preferences: {json.dumps(diet_preferences)}

Generate exactly 1 recipes:
- 1 Breakfast recipes
- 0 Lunch recipes
- 0 Dinner recipes

For each recipe, provide:
- A unique ID (use format: meal_type_number, e.g., "breakfast_1", "lunch_1", "dinner_1")
- A descriptive title
- Preparation time in minutes (realistic estimate from start to finish)
- Calorie count per serving
- Complete list of ingredients with availability (set all to true for now)
- Three versions of step-by-step instructions:
  1. High Level: Brief, concise instructions for experienced cooks
  2. Detailed: Comprehensive instructions with more explanation
  3. Teach Mode: Extremely detailed instructions with explanations of techniques, tips, and cooking science
- A search query for a relevant dish image (use two descriptive words)
- Appropriate meal type

Ensure recipes align with the user's dietary restrictions, preferences, and goals. Make recipes diverse, nutritious, and practical."""

        # Create the prompt
        prompt = """Generate 3 diverse and delicious recipes based on my dietary preferences.
        I want exactly 1 breakfast recipes, 0 lunch recipes, and 0 dinner recipes.
        Make sure each recipe is complete with all required fields and follows my dietary restrictions."""

        # Prepare the API call parameters
        params = {
            "model": "gpt-4.1",
            "input": [{"role": "developer", "content": prompt}],
            "temperature": 0.8,
            "instructions": instructions,
            "text": {
                "format": {
                    "name": "recipes",
                    "schema": recipe_generation_schema,
                    "type": "json_schema"
                }
            },
            "user": uid
        }

        # Call OpenAI API
        response = client.responses.create(**params)

        # Extract the generated recipes
        output_content = response.output_text

        if not output_content:
            return Response(
                json.dumps({"error": "No response content from OpenAI API"}),
                status=500,
                headers={"Content-Type": "application/json"}
            )
        print(output_content)
        # Parse the JSON response
        try:
            parsed_response = json.loads(output_content)
        except json.JSONDecodeError as e:
            return Response(
                json.dumps({"error": f"Failed to parse OpenAI response: {str(e)}"}),
                status=500,
                headers={"Content-Type": "application/json"}
            )

        if not parsed_response.get("recipes") or not isinstance(parsed_response["recipes"], list):
            return Response(
                json.dumps({"error": "Invalid response format from OpenAI"}),
                status=500,
                headers={"Content-Type": "application/json"}
            )

        recipes = parsed_response["recipes"]

        # Store recipes in Firestore
        recipes_ref = db.collection('recipes').document(uid)
        recipes_data = {
            "recipes": recipes,
            "count": len(recipes),
            "generatedAt": firestore.SERVER_TIMESTAMP,
        }

        recipes_ref.set(recipes_data)

        return Response(
            json.dumps({
                "success": True,
                "message": f"Successfully generated and stored {len(recipes)} recipes",
                "recipeCount": len(recipes)
            }),
            status=200,
            headers={"Content-Type": "application/json"}
        )

    except Exception as e:
        return Response(
            json.dumps({"error": f"Internal server error: {str(e)}"}),
            status=500,
            headers={"Content-Type": "application/json"}
        )