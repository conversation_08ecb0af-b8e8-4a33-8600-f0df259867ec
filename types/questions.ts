export interface Option {
    value: string | number;
    label?: string; // Optional, as some options might just be icons/emojis
    emoji?: string;
    icon?: string; // For icon buttons
}

export interface Question {
    id: string; // Changed to string to be more flexible, e.g., 'allergy_selection'
    text: string;
    options: Option[];
    allowMultiple?: boolean; // Keep this if some questions still allow multiple selections
}

// If we need to manage the state of the new onboarding flow, we can add a state interface here later.
// For example:
// export interface OnboardingState {
//     currentQuestionIndex: number;
//     answers: Record<string, string | number | string[] | number[]>; // Keyed by question id
//     isComplete: boolean;
// } 